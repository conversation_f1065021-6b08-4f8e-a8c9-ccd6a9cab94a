{"scanDate": "2025-07-20T12:25:34.866Z", "summary": {"total": 238, "high": 12, "medium": 226, "low": 0}, "duplicates": [{"pattern": "相似文件名: redis", "files": ["src/config/redis.ts", "src/shared/infrastructure/messaging/redis.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: index", "files": ["src/config/index.ts", "src/contexts/user-config/index.ts", "src/application/version-manager/index.ts", "src/application/sync/index.ts", "src/application/real-time-sync/index.ts", "src/application/event-broadcaster/index.ts", "src/api/routes/index.ts", "src/shared/infrastructure/types/index.ts", "src/shared/infrastructure/http/index.ts", "src/shared/infrastructure/data-processing/index.ts", "src/shared/infrastructure/analysis/index.ts", "src/shared/application/dto-mappers/index.ts", "src/shared/infrastructure/logging/interfaces/index.ts", "src/shared/infrastructure/di/types/index.ts", "src/shared/infrastructure/config/interfaces/index.ts", "src/contexts/user-management/application/commands/index.ts", "src/contexts/market-data/infrastructure/websocket/index.ts", "src/shared/infrastructure/data-processing/strategies/risk/index.ts", "src/shared/infrastructure/data-processing/strategies/interfaces/index.ts", "src/shared/infrastructure/analysis/interfaces/pattern-recognition/index.ts"], "severity": "medium", "description": "发现20个相似命名的文件"}, {"pattern": "相似文件名: environment", "files": ["src/config/environment.ts", "src/shared/infrastructure/config/environment/environment-manager.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: database", "files": ["src/config/database.ts", "src/shared/infrastructure/database/database.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: alerting", "files": ["src/application/alerting-service.ts", "src/api/controllers/alerting-controller.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: baseapplication", "files": ["src/shared/application/base-application-service.ts", "src/shared/application/services/base-application-service.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: synctypes", "files": ["src/application/sync/sync-types.ts", "src/application/sync/types/sync-types.ts", "src/application/real-time-sync/types/sync-types.ts"], "severity": "medium", "description": "发现3个相似命名的文件"}, {"pattern": "相似文件名: unifiedhealth", "files": ["src/api/controllers/unified-health-controller.ts", "src/shared/infrastructure/health/unified-health-service.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: trendanalysis", "files": ["src/api/controllers/trend-analysis-controller.ts", "src/contexts/trend-analysis/domain/value-objects/trend-analysis.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: tradingsignals.", "files": ["src/api/controllers/trading-signals.controller.ts", "src/contexts/trading-signals/presentation/controllers/trading-signals.controller.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: strategysync", "files": ["src/api/controllers/strategy-sync-controller.ts", "src/contexts/trading-execution/domain/services/strategy-sync-service.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: riskassessment", "files": ["src/api/controllers/risk-assessment-controller.ts", "src/contexts/risk-management/domain/value-objects/risk-assessment.ts", "src/contexts/risk-management/domain/entities/risk-assessment.ts"], "severity": "medium", "description": "发现3个相似命名的文件"}, {"pattern": "相似文件名: productionsignal", "files": ["src/api/controllers/production-signal-controller.ts", "src/contexts/trading-signals/application/services/production-signal-service.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: dualtrackmonitoring", "files": ["src/api/controllers/dual-track-monitoring-controller.ts", "src/contexts/trading-execution/domain/services/dual-track-monitoring-service.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: auth", "files": ["src/api/controllers/auth-controller.ts", "src/contexts/user-management/presentation/http/controllers/AuthController.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: types", "files": ["src/shared/infrastructure/technical-indicators/types.ts", "src/shared/infrastructure/di/types.ts", "src/shared/infrastructure/ai/types.ts", "src/contexts/trend-analysis/infrastructure/di/types.ts", "src/contexts/market-data/infrastructure/di/types.ts", "src/shared/infrastructure/data-processing/strategies/risk/types.ts"], "severity": "medium", "description": "发现6个相似命名的文件"}, {"pattern": "相似文件名: llmrouter", "files": ["src/shared/infrastructure/llm/llm-router.ts", "src/contexts/ai-reasoning/infrastructure/llm-providers/llm-router.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: llmprovider.interface", "files": ["src/shared/infrastructure/llm/llm-provider.interface.ts", "src/contexts/ai-reasoning/domain/services/llm-provider.interface.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: container", "files": ["src/shared/infrastructure/di/container.ts", "src/contexts/trading-execution/infrastructure/di/container.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: authmiddleware", "files": ["src/shared/infrastructure/auth/auth-middleware.ts", "src/api/middleware/modules/auth-middleware.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: vectorclock", "files": ["src/application/version-manager/clock/vector-clock-manager.ts", "src/contexts/market-data/infrastructure/external/vector-clock-manager.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: userconfigcontainermodule", "files": ["src/shared/infrastructure/di/modules/user-config-container-module.ts", "src/contexts/user-config/infrastructure/di/user-config-container-module.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: strategyfactory", "files": ["src/shared/infrastructure/data-processing/strategies/strategy-factory.ts", "src/contexts/trading-signals/infrastructure/strategies/strategy-factory.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: patternrecognition", "files": ["src/shared/infrastructure/analysis/services/PatternRecognitionService.ts", "src/contexts/trend-analysis/infrastructure/services/pattern-recognition.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: configvalidator", "files": ["src/shared/infrastructure/config/validators/config-validator.ts", "src/shared/infrastructure/config/validation/config-validator.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: apikey", "files": ["src/contexts/user-management/presentation/http/ApiKeyController.ts", "src/contexts/user-management/domain/entities/ApiKey.ts", "src/contexts/user-management/application/services/ApiKeyService.ts"], "severity": "medium", "description": "发现3个相似命名的文件"}, {"pattern": "相似文件名: user", "files": ["src/contexts/user-management/domain/entities/User.ts", "src/contexts/user-management/presentation/http/controllers/UserController.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: risklevel", "files": ["src/contexts/user-management/domain/value-objects/RiskLevel.ts", "src/contexts/trading-signals/domain/value-objects/risk-level.ts", "src/contexts/risk-management/domain/value-objects/risk-level.ts"], "severity": "medium", "description": "发现3个相似命名的文件"}, {"pattern": "相似文件名: userprofile", "files": ["src/contexts/user-config/presentation/controllers/user-profile-controller.ts", "src/contexts/user-config/domain/entities/user-profile.ts", "src/contexts/trading-signals/domain/entities/user-profile.ts"], "severity": "medium", "description": "发现3个相似命名的文件"}, {"pattern": "相似文件名: userpreferences", "files": ["src/contexts/user-config/presentation/controllers/user-preferences-controller.ts", "src/contexts/user-config/domain/entities/user-preferences.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: marketdata", "files": ["src/contexts/trading-signals/domain/entities/market-data.ts", "src/contexts/market-data/presentation/http/market-data-controller.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "相似文件名: position", "files": ["src/contexts/risk-management/domain/value-objects/position.ts", "src/contexts/trading-execution/domain/services/position-manager.ts"], "severity": "medium", "description": "发现2个相似命名的文件"}, {"pattern": "重复类名: TradingSignalsController", "files": ["src/api/controllers/trading-signals.controller.ts", "src/contexts/trading-signals/presentation/controllers/trading-signals.controller.ts"], "severity": "high", "description": "类 TradingSignalsController 在2个文件中定义"}, {"pattern": "重复类名: AuthController", "files": ["src/api/controllers/auth-controller.ts", "src/contexts/user-management/presentation/http/controllers/AuthController.ts"], "severity": "high", "description": "类 AuthController 在2个文件中定义"}, {"pattern": "重复类名: DataMappingService", "files": ["src/shared/infrastructure/database/unified-data-mapper.ts", "src/shared/infrastructure/database/data-mapping-service.ts"], "severity": "high", "description": "类 DataMappingService 在2个文件中定义"}, {"pattern": "重复类名: UniqueEntityId", "files": ["src/shared/domain/value-objects/unique-entity-id.ts", "src/shared/domain/entities/base-entity.ts"], "severity": "high", "description": "类 UniqueEntityId 在2个文件中定义"}, {"pattern": "重复类名: VectorClock", "files": ["src/application/version-manager/clock/vector-clock-manager.ts", "src/contexts/market-data/infrastructure/external/vector-clock-manager.ts"], "severity": "high", "description": "类 VectorClock 在2个文件中定义"}, {"pattern": "重复类名: VectorClockManager", "files": ["src/application/version-manager/clock/vector-clock-manager.ts", "src/contexts/market-data/infrastructure/external/vector-clock-manager.ts"], "severity": "high", "description": "类 VectorClockManager 在2个文件中定义"}, {"pattern": "重复类名: SymbolValidator", "files": ["src/shared/infrastructure/di/base/unified-symbol-registry.ts", "src/contexts/trading-execution/infrastructure/services/symbol-validator.ts"], "severity": "high", "description": "类 SymbolValidator 在2个文件中定义"}, {"pattern": "重复类名: StrategyFactory", "files": ["src/shared/infrastructure/data-processing/strategies/strategy-factory.ts", "src/contexts/trading-signals/infrastructure/strategies/strategy-factory.ts"], "severity": "high", "description": "类 StrategyFactory 在2个文件中定义"}, {"pattern": "重复类名: ConfigValidator", "files": ["src/shared/infrastructure/config/validators/config-validator.ts", "src/shared/infrastructure/config/validation/config-validator.ts"], "severity": "high", "description": "类 ConfigValidator 在2个文件中定义"}, {"pattern": "重复类名: PatternRecognitionService", "files": ["src/shared/infrastructure/analysis/services/PatternRecognitionService.ts", "src/contexts/trend-analysis/infrastructure/services/pattern-recognition.ts"], "severity": "high", "description": "类 PatternRecognitionService 在2个文件中定义"}, {"pattern": "重复类名: RiskLevel", "files": ["src/contexts/user-management/domain/value-objects/RiskLevel.ts", "src/contexts/risk-management/domain/value-objects/risk-level.ts"], "severity": "high", "description": "类 RiskLevel 在2个文件中定义"}, {"pattern": "重复类名: UserModelPreference", "files": ["src/contexts/user-config/domain/entities/UserModelPreference.ts", "src/contexts/user-config/domain/entities/UserLLMConfig.ts"], "severity": "high", "description": "类 UserModelPreference 在2个文件中定义"}, {"pattern": "重复接口: RedisConfig", "files": ["src/config/redis.ts", "src/shared/infrastructure/testing/unified-test-config-manager.ts"], "severity": "medium", "description": "接口 RedisConfig 在2个文件中定义"}, {"pattern": "重复接口: DatabaseConfig", "files": ["src/config/database.ts", "src/shared/infrastructure/testing/unified-test-config-manager.ts", "src/shared/infrastructure/config/unified-performance-config.ts"], "severity": "medium", "description": "接口 DatabaseConfig 在3个文件中定义"}, {"pattern": "重复接口: SyncConfig", "files": ["src/application/data-update-service.ts", "src/shared/infrastructure/order/order-status-sync-service.ts", "src/application/real-time-sync/types/sync-types.ts"], "severity": "medium", "description": "接口 SyncConfig 在3个文件中定义"}, {"pattern": "重复接口: SyncConfiguration", "files": ["src/application/cross-system-state-sync.ts", "src/application/sync/sync-types.ts"], "severity": "medium", "description": "接口 SyncConfiguration 在2个文件中定义"}, {"pattern": "重复接口: SyncResult", "files": ["src/application/cross-system-state-sync.ts", "src/application/RefactoredCrossSystemStateSyncManager.ts", "src/application/sync/sync-types.ts", "src/shared/infrastructure/order/order-status-sync-service.ts", "src/application/real-time-sync/types/sync-types.ts"], "severity": "medium", "description": "接口 SyncResult 在5个文件中定义"}, {"pattern": "重复接口: HealthCheckResult", "files": ["src/shared/application/application-service-interfaces.ts", "src/shared/infrastructure/monitoring/unified-monitoring-manager.ts", "src/shared/infrastructure/health/unified-health-service.ts", "src/contexts/market-data/infrastructure/websocket/monitoring/websocket-health-checker.ts"], "severity": "medium", "description": "接口 HealthCheckResult 在4个文件中定义"}, {"pattern": "重复接口: PerformanceMetrics", "files": ["src/shared/application/application-service-interfaces.ts", "src/application/sync/sync-types.ts", "src/shared/infrastructure/types/unified-interfaces.ts"], "severity": "medium", "description": "接口 PerformanceMetrics 在3个文件中定义"}, {"pattern": "重复接口: IApplicationService", "files": ["src/shared/application/application-service-interfaces.ts", "src/shared/application/interfaces/application-service.ts"], "severity": "medium", "description": "接口 IApplicationService 在2个文件中定义"}, {"pattern": "重复接口: TrendAnalysisRequest", "files": ["src/api/controllers/trend-analysis-controller.ts", "src/contexts/trend-analysis/application/trend-analysis-application.service.ts"], "severity": "medium", "description": "接口 TrendAnalysisRequest 在2个文件中定义"}, {"pattern": "重复接口: TrendAnalysisResponse", "files": ["src/api/controllers/trend-analysis-controller.ts", "src/contexts/trend-analysis/application/trend-analysis-application.service.ts"], "severity": "medium", "description": "接口 TrendAnalysisResponse 在2个文件中定义"}, {"pattern": "重复接口: BatchSignalGenerationRequest", "files": ["src/api/controllers/trading-signals.controller.ts", "src/contexts/trading-signals/domain/interfaces/signal-generation.interface.ts"], "severity": "medium", "description": "接口 BatchSignalGenerationRequest 在2个文件中定义"}, {"pattern": "重复接口: BatchSignalGenerationResponse", "files": ["src/api/controllers/trading-signals.controller.ts", "src/contexts/trading-signals/domain/interfaces/signal-generation.interface.ts"], "severity": "medium", "description": "接口 BatchSignalGenerationResponse 在2个文件中定义"}, {"pattern": "重复接口: TradeRequest", "files": ["src/api/controllers/trading-execution-controller.ts", "src/shared/infrastructure/types/unified-service-interfaces.ts"], "severity": "medium", "description": "接口 TradeRequest 在2个文件中定义"}, {"pattern": "重复接口: RiskAssessmentRequest", "files": ["src/api/controllers/risk-assessment-controller.ts", "src/shared/infrastructure/types/unified-service-interfaces.ts", "src/contexts/risk-management/domain/interfaces/risk-assessment-application-service.interface.ts", "src/contexts/risk-management/application/services/risk-assessment-application-service.ts"], "severity": "medium", "description": "接口 RiskAssessmentRequest 在4个文件中定义"}, {"pattern": "重复接口: RiskAssessmentResponse", "files": ["src/api/controllers/risk-assessment-controller.ts", "src/contexts/risk-management/domain/value-objects/risk-assessment.ts", "src/contexts/risk-management/domain/interfaces/risk-assessment-output.ts", "src/contexts/risk-management/domain/interfaces/risk-assessment-application-service.interface.ts"], "severity": "medium", "description": "接口 RiskAssessmentResponse 在4个文件中定义"}, {"pattern": "重复接口: LoginRequest", "files": ["src/api/controllers/auth-controller.ts", "src/contexts/user-management/application/services/UserManagementApplicationService.ts"], "severity": "medium", "description": "接口 LoginRequest 在2个文件中定义"}, {"pattern": "重复接口: RegisterRequest", "files": ["src/api/controllers/auth-controller.ts", "src/contexts/user-management/application/services/UserManagementApplicationService.ts"], "severity": "medium", "description": "接口 RegisterRequest 在2个文件中定义"}, {"pattern": "重复接口: DataChangeEvent", "files": ["src/application/sync/sync-types.ts", "src/application/event-broadcaster/types/broadcaster-types.ts"], "severity": "medium", "description": "接口 DataChangeEvent 在2个文件中定义"}, {"pattern": "重复接口: SystemStatus", "files": ["src/application/sync/sync-types.ts", "src/application/sync/types/sync-types.ts", "src/contexts/trading-execution/infrastructure/services/emergency-stop-manager.ts"], "severity": "medium", "description": "接口 SystemStatus 在3个文件中定义"}, {"pattern": "重复接口: HealthStatus", "files": ["src/application/sync/sync-types.ts", "src/shared/infrastructure/types/unified-interfaces.ts"], "severity": "medium", "description": "接口 HealthStatus 在2个文件中定义"}, {"pattern": "重复接口: SyncStatistics", "files": ["src/application/sync/sync-types.ts", "src/application/sync/types/sync-types.ts"], "severity": "medium", "description": "接口 SyncStatistics 在2个文件中定义"}, {"pattern": "重复接口: SyncTransformation", "files": ["src/application/sync/sync-types.ts", "src/application/sync/types/sync-types.ts"], "severity": "medium", "description": "接口 SyncTransformation 在2个文件中定义"}, {"pattern": "重复接口: ValidationResult", "files": ["src/application/state-sync/StateDataValidator.ts", "src/shared/application/interfaces/application-service.ts", "src/shared/infrastructure/config/dynamic/dynamic-config.interface.ts", "src/contexts/user-config/domain/services/user-config-validator.ts", "src/contexts/trading-signals/infrastructure/validation/data-authenticity-validator.ts", "src/contexts/trading-execution/domain/interfaces/execution-engine.interface.ts", "src/contexts/ai-reasoning/domain/services/parameter-config-center.interface.ts"], "severity": "medium", "description": "接口 ValidationResult 在7个文件中定义"}, {"pattern": "重复接口: ValidationRule", "files": ["src/application/state-sync/StateDataValidator.ts", "src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/market-data/unified-market-data-processor.ts", "src/shared/infrastructure/config/dynamic/dynamic-config.interface.ts"], "severity": "medium", "description": "接口 ValidationRule 在4个文件中定义"}, {"pattern": "重复接口: IRepositoryBaseService", "files": ["src/shared/domain/repositories/repository-base.interface.ts", "src/shared/infrastructure/database/repository-base-service.interface.ts"], "severity": "medium", "description": "接口 IRepositoryBaseService 在2个文件中定义"}, {"pattern": "重复接口: QueryOptions", "files": ["src/shared/domain/repositories/base-repository.interface.ts", "src/shared/infrastructure/database/repository-base-service.interface.ts"], "severity": "medium", "description": "接口 QueryOptions 在2个文件中定义"}, {"pattern": "重复接口: <PERSON>og<PERSON>", "files": ["src/shared/core/interfaces/ILogger.ts", "src/shared/infrastructure/logging/logger.interface.ts", "src/shared/application/interfaces/logger.ts"], "severity": "medium", "description": "接口 ILogger 在3个文件中定义"}, {"pattern": "重复接口: LogMetadata", "files": ["src/shared/core/interfaces/ILogger.ts", "src/shared/application/interfaces/logger.ts"], "severity": "medium", "description": "接口 LogMetadata 在2个文件中定义"}, {"pattern": "重复接口: PredictionValidationResult", "files": ["src/shared/infrastructure/validation/price-validation-engine.ts", "src/contexts/ai-reasoning/domain/services/unified-prediction-engine.interface.ts"], "severity": "medium", "description": "接口 PredictionValidationResult 在2个文件中定义"}, {"pattern": "重复接口: ServiceHealthStatus", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/shared/application/interfaces/external-service.ts", "src/contexts/market-data/infrastructure/external/binance-adapter.ts"], "severity": "medium", "description": "接口 ServiceHealthStatus 在3个文件中定义"}, {"pattern": "重复接口: DataQualityReport", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/shared/infrastructure/data-quality/unified-data-quality-monitor.ts"], "severity": "medium", "description": "接口 DataQualityReport 在2个文件中定义"}, {"pattern": "重复接口: ConflictResolutionResult", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/contexts/market-data/infrastructure/external/data-source-priority-resolver.ts"], "severity": "medium", "description": "接口 ConflictResolutionResult 在2个文件中定义"}, {"pattern": "重复接口: DataQualityIssue", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/contexts/trend-analysis/infrastructure/services/data-quality-validator.ts"], "severity": "medium", "description": "接口 DataQualityIssue 在2个文件中定义"}, {"pattern": "重复接口: ConfigValidationResult", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/shared/infrastructure/config/validation/config-validation-rules.ts", "src/shared/infrastructure/config/interfaces/config-validation.interface.ts"], "severity": "medium", "description": "接口 ConfigValidationResult 在3个文件中定义"}, {"pattern": "重复接口: PatternRecognitionResult", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/shared/infrastructure/analysis/types/PatternTypes.ts"], "severity": "medium", "description": "接口 PatternRecognitionResult 在2个文件中定义"}, {"pattern": "重复接口: TrendAnalysisData", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/contexts/learning/infrastructure/analyzers/trend-consistency-analyzer.ts"], "severity": "medium", "description": "接口 TrendAnalysisData 在2个文件中定义"}, {"pattern": "重复接口: TrendAnalysisResult", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/contexts/trend-analysis/application/trend-analysis-application.service.ts"], "severity": "medium", "description": "接口 TrendAnalysisResult 在2个文件中定义"}, {"pattern": "重复接口: AnalysisContext", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts"], "severity": "medium", "description": "接口 AnalysisContext 在2个文件中定义"}, {"pattern": "重复接口: SignalGenerationRequest", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/contexts/trading-signals/domain/interfaces/signal-generation.interface.ts", "src/contexts/trading-signals/application/services/signal-generation-application-service.ts"], "severity": "medium", "description": "接口 SignalGenerationRequest 在3个文件中定义"}, {"pattern": "重复接口: TradingSignal", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/shared/infrastructure/testing/unified-test-data-generator.ts", "src/contexts/trading-execution/domain/services/trading-strategy-engine.ts", "src/contexts/learning/infrastructure/analyzers/signal-quality-analyzer.ts", "src/contexts/market-data/infrastructure/external/tokenmetrics-adapter.ts"], "severity": "medium", "description": "接口 TradingSignal 在5个文件中定义"}, {"pattern": "重复接口: StrategyExecutionResult", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/contexts/trading-signals/domain/interfaces/trading-strategy.interface.ts", "src/contexts/market-data/infrastructure/external/conflict-resolution-strategy-engine.ts"], "severity": "medium", "description": "接口 StrategyExecutionResult 在3个文件中定义"}, {"pattern": "重复接口: RiskCheckResult", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/contexts/trading-execution/domain/interfaces/execution-engine.interface.ts"], "severity": "medium", "description": "接口 RiskCheckResult 在2个文件中定义"}, {"pattern": "重复接口: OrderResult", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/contexts/trading-execution/domain/services/order-executor.ts"], "severity": "medium", "description": "接口 OrderResult 在2个文件中定义"}, {"pattern": "重复接口: PositionAction", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/contexts/trading-execution/domain/services/position-manager.ts"], "severity": "medium", "description": "接口 PositionAction 在2个文件中定义"}, {"pattern": "重复接口: StrategyPerformance", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/contexts/ai-reasoning/domain/services/knowledge-graph.interface.ts"], "severity": "medium", "description": "接口 StrategyPerformance 在2个文件中定义"}, {"pattern": "重复接口: RiskViolation", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/shared/infrastructure/risk/risk-enforcement-engine.ts"], "severity": "medium", "description": "接口 RiskViolation 在2个文件中定义"}, {"pattern": "重复接口: RiskCalculationResult", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/shared/infrastructure/data-processing/strategies/risk/risk-calculation-stage.ts"], "severity": "medium", "description": "接口 RiskCalculationResult 在2个文件中定义"}, {"pattern": "重复接口: Risk<PERSON><PERSON>t", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/shared/infrastructure/risk/real-time-risk-monitor.ts", "src/contexts/trading-execution/domain/services/risk-monitor.ts"], "severity": "medium", "description": "接口 RiskAlert 在3个文件中定义"}, {"pattern": "重复接口: MarketData", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/shared/infrastructure/testing/unified-test-data-generator.ts", "src/contexts/market-data/domain/services/external-adapters/ICoinMetricsAdapter.ts"], "severity": "medium", "description": "接口 MarketData 在3个文件中定义"}, {"pattern": "重复接口: RiskMetrics", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/shared/infrastructure/data-processing/strategies/risk/types.ts"], "severity": "medium", "description": "接口 RiskMetrics 在2个文件中定义"}, {"pattern": "重复接口: StressTestResult", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts", "src/contexts/risk-management/domain/services/ai-risk-analysis-engine.interface.ts", "src/contexts/learning/infrastructure/optimizers/parameter-optimizer.ts"], "severity": "medium", "description": "接口 StressTestResult 在4个文件中定义"}, {"pattern": "重复接口: TimeRange", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts", "src/contexts/market-data/domain/services/real-time-data-quality-monitor.interface.ts"], "severity": "medium", "description": "接口 TimeRange 在4个文件中定义"}, {"pattern": "重复接口: TrendPrediction", "files": ["src/shared/infrastructure/types/unified-service-interfaces.ts", "src/shared/infrastructure/types/unified-interfaces.ts", "src/contexts/trend-analysis/domain/value-objects/trend-analysis.ts", "src/contexts/trend-analysis/domain/services/trend-prediction.interface.ts", "src/contexts/trend-analysis/domain/services/trend-analysis-engine.interface.ts"], "severity": "medium", "description": "接口 TrendPrediction 在5个文件中定义"}, {"pattern": "重复接口: CacheEntry", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/contexts/trend-analysis/infrastructure/services/performance-optimizer.ts"], "severity": "medium", "description": "接口 CacheEntry 在2个文件中定义"}, {"pattern": "重复接口: CacheStats", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/contexts/market-data/domain/services/multi-layer-cache-system.interface.ts"], "severity": "medium", "description": "接口 CacheStats 在2个文件中定义"}, {"pattern": "重复接口: SystemMetrics", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/monitoring/unified-monitoring-manager.ts", "src/shared/infrastructure/monitoring/interfaces/unified-monitoring-interfaces.ts"], "severity": "medium", "description": "接口 SystemMetrics 在3个文件中定义"}, {"pattern": "重复接口: AlertStats", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/monitoring/unified-alert-system.ts", "src/contexts/risk-management/domain/repositories/alert-repository.ts"], "severity": "medium", "description": "接口 AlertStats 在3个文件中定义"}, {"pattern": "重复接口: ResourceMetrics", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/performance/concurrency-optimizer.ts", "src/shared/infrastructure/monitoring/interfaces/unified-monitoring-interfaces.ts", "src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts"], "severity": "medium", "description": "接口 ResourceMetrics 在4个文件中定义"}, {"pattern": "重复接口: LearningInsights", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/contexts/learning/infrastructure/engines/experience-engine.ts", "src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts", "src/contexts/ai-reasoning/domain/services/learning-analysis.interface.ts"], "severity": "medium", "description": "接口 LearningInsights 在4个文件中定义"}, {"pattern": "重复接口: MarketOutcome", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/contexts/learning/infrastructure/engines/experience-engine.ts", "src/contexts/ai-reasoning/domain/services/unified-learning-engine.interface.ts"], "severity": "medium", "description": "接口 MarketOutcome 在3个文件中定义"}, {"pattern": "重复接口: TimeframeIsolatedParameters", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/contexts/ai-reasoning/infrastructure/services/multi-timeframe-learning-coordinator.ts", "src/contexts/ai-reasoning/domain/services/timeframe-coordinator.interface.ts"], "severity": "medium", "description": "接口 TimeframeIsolatedParameters 在3个文件中定义"}, {"pattern": "重复接口: DataQualityMetrics", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/contexts/market-data/infrastructure/monitoring/data-quality-monitor.ts", "src/contexts/trend-analysis/infrastructure/services/data-source-health-monitor.ts"], "severity": "medium", "description": "接口 DataQualityMetrics 在3个文件中定义"}, {"pattern": "重复接口: ComponentHealthCheck", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/health/unified-health-service.ts", "src/shared/infrastructure/monitoring/interfaces/unified-monitoring-interfaces.ts"], "severity": "medium", "description": "接口 ComponentHealthCheck 在3个文件中定义"}, {"pattern": "重复接口: DependencyHealth", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/health/unified-health-service.ts", "src/shared/infrastructure/monitoring/interfaces/unified-monitoring-interfaces.ts"], "severity": "medium", "description": "接口 DependencyHealth 在3个文件中定义"}, {"pattern": "重复接口: RiskLevelProps", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/contexts/user-management/domain/value-objects/RiskLevel.ts"], "severity": "medium", "description": "接口 RiskLevelProps 在2个文件中定义"}, {"pattern": "重复接口: GeometricPattern", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/contexts/trend-analysis/infrastructure/services/ai-pattern-analyzer.ts", "src/contexts/trend-analysis/infrastructure/modules/ai-enhancement/pattern-ai-enhancer.ts"], "severity": "medium", "description": "接口 GeometricPattern 在3个文件中定义"}, {"pattern": "重复接口: FetchOptions", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/data-processing/interfaces/IExternalDataAdapter.ts"], "severity": "medium", "description": "接口 FetchOptions 在2个文件中定义"}, {"pattern": "重复接口: BatchOperationOptions", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/database/query-manager.ts"], "severity": "medium", "description": "接口 BatchOperationOptions 在2个文件中定义"}, {"pattern": "重复接口: SemanticCacheOptions", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/ai/types.ts"], "severity": "medium", "description": "接口 SemanticCacheOptions 在2个文件中定义"}, {"pattern": "重复接口: PaginationOptions", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/ai/types.ts"], "severity": "medium", "description": "接口 PaginationOptions 在2个文件中定义"}, {"pattern": "重复接口: PaginatedResult", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/database/repository-base-service.interface.ts", "src/shared/infrastructure/ai/types.ts", "src/shared/application/interfaces/repository.ts"], "severity": "medium", "description": "接口 PaginatedResult 在4个文件中定义"}, {"pattern": "重复接口: AlertRule", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts"], "severity": "medium", "description": "接口 AlertRule 在2个文件中定义"}, {"pattern": "重复接口: <PERSON>ert", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts"], "severity": "medium", "description": "接口 Alert 在2个文件中定义"}, {"pattern": "重复接口: MonitoringPerformanceMetrics", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/monitoring/interfaces/unified-monitoring-interfaces.ts"], "severity": "medium", "description": "接口 MonitoringPerformanceMetrics 在2个文件中定义"}, {"pattern": "重复接口: VersionConflict", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/application/version-manager/types/version-types.ts", "src/contexts/market-data/infrastructure/external/vector-clock-manager.ts"], "severity": "medium", "description": "接口 VersionConflict 在3个文件中定义"}, {"pattern": "重复接口: PositionInfo", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/contexts/trading-execution/domain/interfaces/execution-engine.interface.ts"], "severity": "medium", "description": "接口 PositionInfo 在2个文件中定义"}, {"pattern": "重复接口: OptimizationSuggestion", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/cache/cache-performance-monitor.ts"], "severity": "medium", "description": "接口 OptimizationSuggestion 在2个文件中定义"}, {"pattern": "重复接口: MarketCondition", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/analysis/types/WeightingTypes.ts", "src/contexts/learning/infrastructure/engines/experience-engine.ts", "src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts", "src/contexts/trend-analysis/infrastructure/config/dynamic-parameter-manager.ts"], "severity": "medium", "description": "接口 MarketCondition 在5个文件中定义"}, {"pattern": "重复接口: TimeframeWeights", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/analysis/types/WeightingTypes.ts", "src/contexts/learning/infrastructure/optimizers/parameter-optimizer.ts"], "severity": "medium", "description": "接口 TimeframeWeights 在3个文件中定义"}, {"pattern": "重复接口: WavePoint", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/analysis/algorithms/ElliottWaveAlgorithms.ts", "src/contexts/trend-analysis/infrastructure/services/target-stop-calculator.ts", "src/contexts/trend-analysis/infrastructure/services/professional-corrective-wave-analyzer.ts", "src/contexts/trend-analysis/infrastructure/services/fibonacci-analyzer.ts"], "severity": "medium", "description": "接口 WavePoint 在5个文件中定义"}, {"pattern": "重复接口: QualityMetrics", "files": ["src/shared/infrastructure/types/unified-interfaces.ts", "src/shared/infrastructure/data-processing/interfaces/IDataProcessingPipeline.ts", "src/contexts/market-data/infrastructure/external/exchange-router.ts", "src/contexts/market-data/domain/services/real-time-data-quality-monitor.interface.ts"], "severity": "medium", "description": "接口 QualityMetrics 在4个文件中定义"}, {"pattern": "重复接口: KlineDataPoint", "files": ["src/shared/infrastructure/testing/unified-test-data-generator.ts", "src/shared/infrastructure/analysis/types/PatternTypes.ts", "src/contexts/trend-analysis/domain/value-objects/multi-timeframe-data.ts"], "severity": "medium", "description": "接口 KlineDataPoint 在3个文件中定义"}, {"pattern": "重复接口: TestContainer", "files": ["src/shared/infrastructure/testing/unified-integration-test-base.ts", "src/shared/infrastructure/testing/types.ts"], "severity": "medium", "description": "接口 TestContainer 在2个文件中定义"}, {"pattern": "重复接口: CacheConfig", "files": ["src/shared/infrastructure/performance/cache-manager.ts", "src/shared/infrastructure/config/unified-performance-config.ts"], "severity": "medium", "description": "接口 CacheConfig 在2个文件中定义"}, {"pattern": "重复接口: PerformanceStats", "files": ["src/shared/infrastructure/monitoring/enhanced-trading-execution-monitor.ts", "src/contexts/ai-reasoning/infrastructure/monitoring/reasoning-performance-monitor.ts"], "severity": "medium", "description": "接口 PerformanceStats 在2个文件中定义"}, {"pattern": "重复接口: AnomalyDetectionResult", "files": ["src/shared/infrastructure/monitoring/enhanced-trading-execution-monitor.ts", "src/contexts/trading-execution/domain/services/dual-track-monitoring-service.ts", "src/contexts/market-data/infrastructure/external/data-anomaly-detection-engine.ts"], "severity": "medium", "description": "接口 AnomalyDetectionResult 在3个文件中定义"}, {"pattern": "重复接口: MonitoringConfig", "files": ["src/shared/infrastructure/monitoring/enhanced-trading-execution-monitor.ts", "src/shared/infrastructure/config/unified-performance-config.ts", "src/shared/infrastructure/monitoring/interfaces/unified-monitoring-interfaces.ts"], "severity": "medium", "description": "接口 MonitoringConfig 在3个文件中定义"}, {"pattern": "重复接口: CacheConsistencyConfig", "files": ["src/shared/infrastructure/market-data/unified-market-data-processor.ts", "src/shared/infrastructure/cache/cache-consistency-manager.ts"], "severity": "medium", "description": "接口 CacheConsistencyConfig 在2个文件中定义"}, {"pattern": "重复接口: ExternalApiClientConfig", "files": ["src/shared/infrastructure/http/http-interfaces.ts", "src/shared/application/interfaces/external-service.ts"], "severity": "medium", "description": "接口 ExternalApiClientConfig 在2个文件中定义"}, {"pattern": "重复接口: HealthCheckConfig", "files": ["src/shared/infrastructure/health/unified-health-service.ts", "src/contexts/market-data/infrastructure/websocket/monitoring/websocket-health-checker.ts"], "severity": "medium", "description": "接口 HealthCheckConfig 在2个文件中定义"}, {"pattern": "重复接口: IHealthCheckProvider", "files": ["src/shared/infrastructure/health/health-check-aggregator.ts", "src/shared/infrastructure/monitoring/interfaces/unified-monitoring-interfaces.ts"], "severity": "medium", "description": "接口 IHealthCheckProvider 在2个文件中定义"}, {"pattern": "重复接口: QualityTrendAnalysis", "files": ["src/shared/infrastructure/data-quality/unified-data-quality-monitor.ts", "src/contexts/market-data/infrastructure/external/data-quality-analysis-engine.ts"], "severity": "medium", "description": "接口 QualityTrendAnalysis 在2个文件中定义"}, {"pattern": "重复接口: DashboardData", "files": ["src/shared/infrastructure/data-quality/data-quality-dashboard.ts", "src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts"], "severity": "medium", "description": "接口 DashboardData 在2个文件中定义"}, {"pattern": "重复接口: QualityAlert", "files": ["src/shared/infrastructure/data-quality/data-quality-dashboard.ts", "src/contexts/market-data/infrastructure/external/real-time-data-quality-monitor.ts", "src/contexts/market-data/domain/services/real-time-data-quality-monitor.interface.ts"], "severity": "medium", "description": "接口 QualityAlert 在3个文件中定义"}, {"pattern": "重复接口: ConfigItem", "files": ["src/shared/infrastructure/config/unified-config-manager.ts", "src/shared/infrastructure/config/dynamic/dynamic-config.interface.ts"], "severity": "medium", "description": "接口 ConfigItem 在2个文件中定义"}, {"pattern": "重复接口: RiskCalculationConfig", "files": ["src/shared/infrastructure/config/risk-config.interface.ts", "src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts"], "severity": "medium", "description": "接口 RiskCalculationConfig 在2个文件中定义"}, {"pattern": "重复接口: FinancialMetricsConfig", "files": ["src/shared/infrastructure/config/risk-config.interface.ts", "src/shared/infrastructure/analysis/interfaces/IFinancialMetricsService.ts"], "severity": "medium", "description": "接口 FinancialMetricsConfig 在2个文件中定义"}, {"pattern": "重复接口: MarketContext", "files": ["src/shared/infrastructure/ai/unified-similarity-service.ts", "src/shared/infrastructure/ai/types.ts", "src/contexts/learning/infrastructure/analyzers/trend-consistency-analyzer.ts", "src/contexts/learning/infrastructure/analyzers/signal-quality-analyzer.ts", "src/contexts/ai-reasoning/domain/services/unified-learning-engine.interface.ts", "src/contexts/ai-reasoning/domain/services/short-cycle-learning.interface.ts", "src/contexts/ai-reasoning/domain/services/learning-knowledge-base.interface.ts", "src/contexts/ai-reasoning/domain/services/learning-analysis.interface.ts", "src/contexts/ai-reasoning/domain/services/knowledge-graph.interface.ts", "src/contexts/trend-analysis/infrastructure/services/target-stop-calculator.ts", "src/contexts/trend-analysis/infrastructure/services/confidence-calculator.ts"], "severity": "medium", "description": "接口 MarketContext 在11个文件中定义"}, {"pattern": "重复接口: ValidationError", "files": ["src/shared/application/interfaces/application-service.ts", "src/shared/infrastructure/config/validation/config-validator.ts"], "severity": "medium", "description": "接口 ValidationError 在2个文件中定义"}, {"pattern": "重复接口: IAuthorizationService", "files": ["src/shared/application/interfaces/application-service.ts", "src/contexts/user-management/domain/services/IAuthorizationService.ts"], "severity": "medium", "description": "接口 IAuthorizationService 在2个文件中定义"}, {"pattern": "重复接口: IAuditService", "files": ["src/shared/application/interfaces/application-service.ts", "src/contexts/user-management/domain/services/IAuditService.ts"], "severity": "medium", "description": "接口 IAuditService 在2个文件中定义"}, {"pattern": "重复接口: RealTimePriceData", "files": ["src/shared/application/dto-mappers/market-data-dto-mappers.ts", "src/contexts/market-data/domain/services/exchange-adapter.ts"], "severity": "medium", "description": "接口 RealTimePriceData 在2个文件中定义"}, {"pattern": "重复接口: VersionedData", "files": ["src/application/version-manager/types/version-types.ts", "src/contexts/market-data/infrastructure/external/vector-clock-manager.ts"], "severity": "medium", "description": "接口 VersionedData 在2个文件中定义"}, {"pattern": "重复接口: CleanupResult", "files": ["src/application/version-manager/types/version-types.ts", "src/shared/infrastructure/config/dynamic/dynamic-config.interface.ts"], "severity": "medium", "description": "接口 CleanupResult 在2个文件中定义"}, {"pattern": "重复接口: LatencyMetrics", "files": ["src/shared/infrastructure/monitoring/interfaces/unified-monitoring-interfaces.ts", "src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts"], "severity": "medium", "description": "接口 LatencyMetrics 在2个文件中定义"}, {"pattern": "重复接口: PerformanceReport", "files": ["src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts", "src/shared/infrastructure/di/base/di-performance-monitor.ts"], "severity": "medium", "description": "接口 PerformanceReport 在2个文件中定义"}, {"pattern": "重复接口: PerformanceSummary", "files": ["src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts", "src/shared/infrastructure/di/base/di-performance-monitor.ts"], "severity": "medium", "description": "接口 PerformanceSummary 在2个文件中定义"}, {"pattern": "重复接口: ReportAttachment", "files": ["src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts", "src/contexts/ai-reasoning/domain/services/reasoning-traceability.interface.ts"], "severity": "medium", "description": "接口 ReportAttachment 在2个文件中定义"}, {"pattern": "重复接口: AdapterHealthStatus", "files": ["src/shared/infrastructure/data-processing/interfaces/IExternalDataAdapter.ts", "src/contexts/market-data/domain/services/external-adapters/IFearGreedAdapter.ts", "src/contexts/market-data/domain/services/external-adapters/ICoinMetricsAdapter.ts"], "severity": "medium", "description": "接口 AdapterHealthStatus 在3个文件中定义"}, {"pattern": "重复接口: ConfigValidationRule", "files": ["src/shared/infrastructure/config/validation/config-validation-rules.ts", "src/shared/infrastructure/config/interfaces/config-validation.interface.ts"], "severity": "medium", "description": "接口 ConfigValidationRule 在2个文件中定义"}, {"pattern": "重复接口: TotpOptions", "files": ["src/contexts/user-management/infrastructure/services/TotpService.ts", "src/contexts/user-management/domain/services/IMfaService.ts", "src/contexts/user-management/domain/services/mfa/IMfaVerificationService.ts", "src/contexts/user-management/domain/services/mfa/IMfaCodeGenerator.ts"], "severity": "medium", "description": "接口 TotpOptions 在4个文件中定义"}, {"pattern": "重复接口: SmsOptions", "files": ["src/contexts/user-management/infrastructure/services/SmsService.ts", "src/contexts/user-management/domain/services/IMfaService.ts", "src/contexts/user-management/domain/services/mfa/IMfaCodeGenerator.ts"], "severity": "medium", "description": "接口 SmsOptions 在3个文件中定义"}, {"pattern": "重复接口: EmailOptions", "files": ["src/contexts/user-management/infrastructure/services/EmailVerificationService.ts", "src/contexts/user-management/domain/services/IMfaService.ts", "src/contexts/user-management/domain/services/mfa/IMfaCodeGenerator.ts"], "severity": "medium", "description": "接口 EmailOptions 在3个文件中定义"}, {"pattern": "重复接口: ThreatDetectionRequest", "files": ["src/contexts/user-management/domain/services/IThreatDetectionService.ts", "src/contexts/user-management/domain/services/threat-detection/IThreatDetector.ts"], "severity": "medium", "description": "接口 ThreatDetectionRequest 在2个文件中定义"}, {"pattern": "重复接口: ThreatDetectionResult", "files": ["src/contexts/user-management/domain/services/IThreatDetectionService.ts", "src/contexts/user-management/domain/services/threat-detection/IThreatDetector.ts"], "severity": "medium", "description": "接口 ThreatDetectionResult 在2个文件中定义"}, {"pattern": "重复接口: ThreatIndicator", "files": ["src/contexts/user-management/domain/services/IThreatDetectionService.ts", "src/contexts/user-management/domain/services/threat-detection/IThreatDetector.ts"], "severity": "medium", "description": "接口 ThreatIndicator 在2个文件中定义"}, {"pattern": "重复接口: UserBehaviorAnalysisRequest", "files": ["src/contexts/user-management/domain/services/IThreatDetectionService.ts", "src/contexts/user-management/domain/services/threat-detection/IBehaviorAnalyzer.ts"], "severity": "medium", "description": "接口 UserBehaviorAnalysisRequest 在2个文件中定义"}, {"pattern": "重复接口: UserBehaviorAnalysisResult", "files": ["src/contexts/user-management/domain/services/IThreatDetectionService.ts", "src/contexts/user-management/domain/services/threat-detection/IBehaviorAnalyzer.ts"], "severity": "medium", "description": "接口 UserBehaviorAnalysisResult 在2个文件中定义"}, {"pattern": "重复接口: BehaviorAnomaly", "files": ["src/contexts/user-management/domain/services/IThreatDetectionService.ts", "src/contexts/user-management/domain/services/threat-detection/IBehaviorAnalyzer.ts"], "severity": "medium", "description": "接口 BehaviorAnomaly 在2个文件中定义"}, {"pattern": "重复接口: BehaviorPattern", "files": ["src/contexts/user-management/domain/services/IThreatDetectionService.ts", "src/contexts/user-management/domain/services/threat-detection/IBehaviorAnalyzer.ts"], "severity": "medium", "description": "接口 BehaviorPattern 在2个文件中定义"}, {"pattern": "重复接口: RiskFactor", "files": ["src/contexts/user-management/domain/services/IThreatDetectionService.ts", "src/contexts/risk-management/domain/value-objects/risk-assessment.ts", "src/contexts/ai-reasoning/domain/services/reasoning-traceability.interface.ts", "src/contexts/user-management/domain/services/threat-detection/IBehaviorAnalyzer.ts"], "severity": "medium", "description": "接口 RiskFactor 在4个文件中定义"}, {"pattern": "重复接口: UserBaseline", "files": ["src/contexts/user-management/domain/services/IThreatDetectionService.ts", "src/contexts/user-management/domain/services/threat-detection/IBehaviorAnalyzer.ts"], "severity": "medium", "description": "接口 UserBaseline 在2个文件中定义"}, {"pattern": "重复接口: IpReputationResult", "files": ["src/contexts/user-management/domain/services/IThreatDetectionService.ts", "src/contexts/user-management/domain/services/threat-detection/IReputationAnalyzer.ts"], "severity": "medium", "description": "接口 IpReputationResult 在2个文件中定义"}, {"pattern": "重复接口: DeviceFingerprintResult", "files": ["src/contexts/user-management/domain/services/IThreatDetectionService.ts", "src/contexts/user-management/domain/services/threat-detection/IReputationAnalyzer.ts"], "severity": "medium", "description": "接口 DeviceFingerprintResult 在2个文件中定义"}, {"pattern": "重复接口: MfaSetupRequest", "files": ["src/contexts/user-management/domain/services/IMfaService.ts", "src/contexts/user-management/domain/services/mfa/IMfaDeviceManager.ts"], "severity": "medium", "description": "接口 MfaSetupRequest 在2个文件中定义"}, {"pattern": "重复接口: MfaSetupResponse", "files": ["src/contexts/user-management/domain/services/IMfaService.ts", "src/contexts/user-management/domain/services/mfa/IMfaDeviceManager.ts"], "severity": "medium", "description": "接口 MfaSetupResponse 在2个文件中定义"}, {"pattern": "重复接口: MfaVerificationRequest", "files": ["src/contexts/user-management/domain/services/IMfaService.ts", "src/contexts/user-management/domain/services/mfa/IMfaVerificationService.ts"], "severity": "medium", "description": "接口 MfaVerificationRequest 在2个文件中定义"}, {"pattern": "重复接口: MfaVerificationResponse", "files": ["src/contexts/user-management/domain/services/IMfaService.ts", "src/contexts/user-management/domain/services/mfa/IMfaVerificationService.ts"], "severity": "medium", "description": "接口 MfaVerificationResponse 在2个文件中定义"}, {"pattern": "重复接口: BackupCodesOptions", "files": ["src/contexts/user-management/domain/services/IMfaService.ts", "src/contexts/user-management/domain/services/mfa/IMfaCodeGenerator.ts"], "severity": "medium", "description": "接口 BackupCodesOptions 在2个文件中定义"}, {"pattern": "重复接口: UserProfileResponse", "files": ["src/contexts/user-management/application/services/UserManagementApplicationService.ts", "src/contexts/user-config/application/services/user-profile-application-service.ts"], "severity": "medium", "description": "接口 UserProfileResponse 在2个文件中定义"}, {"pattern": "重复接口: AggregationResult", "files": ["src/shared/infrastructure/analysis/types/TimeframeTypes.ts", "src/contexts/market-data/infrastructure/external/high-performance-data-aggregator.ts"], "severity": "medium", "description": "接口 AggregationResult 在2个文件中定义"}, {"pattern": "重复接口: DataQualityAssessment", "files": ["src/shared/infrastructure/analysis/types/TimeframeTypes.ts", "src/contexts/market-data/infrastructure/external/real-time-data-quality-monitor.ts"], "severity": "medium", "description": "接口 DataQualityAssessment 在2个文件中定义"}, {"pattern": "重复接口: UserProfile", "files": ["src/contexts/user-config/application/services/UserConfigService.ts", "src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts"], "severity": "medium", "description": "接口 UserProfile 在2个文件中定义"}, {"pattern": "重复接口: IUserModelPreferenceRepository", "files": ["src/contexts/user-config/domain/repositories/IUserModelPreferenceRepository.ts", "src/contexts/user-config/domain/repositories/IUserLLMConfigRepository.ts"], "severity": "medium", "description": "接口 IUserModelPreferenceRepository 在2个文件中定义"}, {"pattern": "重复接口: DataSource", "files": ["src/contexts/trading-signals/infrastructure/validation/data-authenticity-validator.ts", "src/contexts/market-data/infrastructure/external/robust-websocket-adapter.ts", "src/contexts/market-data/infrastructure/external/data-source-priority-resolver.ts"], "severity": "medium", "description": "接口 DataSource 在3个文件中定义"}, {"pattern": "重复接口: SignalGenerationResponse", "files": ["src/contexts/trading-signals/domain/interfaces/signal-generation.interface.ts", "src/contexts/trading-signals/application/services/signal-generation-application-service.ts"], "severity": "medium", "description": "接口 SignalGenerationResponse 在2个文件中定义"}, {"pattern": "重复接口: ApiCredentials", "files": ["src/contexts/trading-execution/domain/types/dual-track.types.ts", "src/contexts/trading-execution/domain/entities/trading-account.ts"], "severity": "medium", "description": "接口 ApiCredentials 在2个文件中定义"}, {"pattern": "重复接口: SyncSettings", "files": ["src/contexts/trading-execution/domain/types/dual-track.types.ts", "src/contexts/trading-execution/domain/entities/trading-account.ts"], "severity": "medium", "description": "接口 SyncSettings 在2个文件中定义"}, {"pattern": "重复接口: PerformanceComparison", "files": ["src/contexts/trading-execution/domain/types/dual-track.types.ts", "src/contexts/trading-execution/domain/services/dual-track-monitoring-service.ts"], "severity": "medium", "description": "接口 PerformanceComparison 在2个文件中定义"}, {"pattern": "重复接口: StrategyValidationResult", "files": ["src/contexts/trading-execution/domain/services/strategy-sync-service.ts", "src/shared/infrastructure/data-processing/strategies/interfaces/strategy-validation.interface.ts"], "severity": "medium", "description": "接口 StrategyValidationResult 在2个文件中定义"}, {"pattern": "重复接口: MarketDepth", "files": ["src/contexts/trading-execution/domain/services/real-slippage-calculator.ts", "src/contexts/trading-execution/domain/interfaces/execution-engine.interface.ts"], "severity": "medium", "description": "接口 MarketDepth 在2个文件中定义"}, {"pattern": "重复接口: OrderExecutionRequest", "files": ["src/contexts/trading-execution/domain/services/real-order-execution-engine.ts", "src/contexts/trading-execution/domain/interfaces/execution-engine.interface.ts"], "severity": "medium", "description": "接口 OrderExecutionRequest 在2个文件中定义"}, {"pattern": "重复接口: OrderExecutionResult", "files": ["src/contexts/trading-execution/domain/services/real-order-execution-engine.ts", "src/contexts/trading-execution/domain/interfaces/execution-engine.interface.ts"], "severity": "medium", "description": "接口 OrderExecutionResult 在2个文件中定义"}, {"pattern": "重复接口: PositionOpenRequest", "files": ["src/contexts/trading-execution/domain/services/order-executor.ts", "src/contexts/trading-execution/domain/interfaces/execution-engine.interface.ts"], "severity": "medium", "description": "接口 PositionOpenRequest 在2个文件中定义"}, {"pattern": "重复接口: PositionOpenResult", "files": ["src/contexts/trading-execution/domain/services/order-executor.ts", "src/contexts/trading-execution/domain/interfaces/execution-engine.interface.ts"], "severity": "medium", "description": "接口 PositionOpenResult 在2个文件中定义"}, {"pattern": "重复接口: MarketDataInput", "files": ["src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts", "src/shared/infrastructure/data-processing/strategies/risk/types.ts"], "severity": "medium", "description": "接口 MarketDataInput 在2个文件中定义"}, {"pattern": "重复接口: StressTestScenario", "files": ["src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts", "src/contexts/risk-management/domain/services/ai-risk-analysis-engine.interface.ts"], "severity": "medium", "description": "接口 StressTestScenario 在2个文件中定义"}, {"pattern": "重复接口: RiskAssessment", "files": ["src/contexts/risk-management/domain/value-objects/risk-assessment.ts", "src/contexts/learning/infrastructure/generators/improvement-suggestion-generator.ts", "src/contexts/ai-reasoning/domain/services/reasoning-traceability.interface.ts"], "severity": "medium", "description": "接口 RiskAssessment 在3个文件中定义"}, {"pattern": "重复接口: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "files": ["src/contexts/risk-management/domain/value-objects/risk-assessment.ts", "src/contexts/learning/infrastructure/generators/improvement-suggestion-generator.ts", "src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts"], "severity": "medium", "description": "接口 AlertThreshold 在3个文件中定义"}, {"pattern": "重复接口: Position", "files": ["src/contexts/risk-management/domain/value-objects/account-info.ts", "src/contexts/ai-reasoning/domain/services/reasoning-traceability.interface.ts", "src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts"], "severity": "medium", "description": "接口 Position 在3个文件中定义"}, {"pattern": "重复接口: BatchRiskAssessmentResponse", "files": ["src/contexts/risk-management/domain/interfaces/risk-assessment-output.ts", "src/contexts/risk-management/domain/interfaces/risk-assessment-application-service.interface.ts", "src/contexts/risk-management/application/services/risk-assessment-application-service.ts"], "severity": "medium", "description": "接口 BatchRiskAssessmentResponse 在3个文件中定义"}, {"pattern": "重复接口: BatchRiskAssessmentRequest", "files": ["src/contexts/risk-management/domain/interfaces/risk-assessment-application-service.interface.ts", "src/contexts/risk-management/application/services/risk-assessment-application-service.ts"], "severity": "medium", "description": "接口 BatchRiskAssessmentRequest 在2个文件中定义"}, {"pattern": "重复接口: ParameterOptimizationResult", "files": ["src/contexts/learning/infrastructure/optimizers/parameter-optimizer.ts", "src/contexts/ai-reasoning/domain/services/unified-learning-engine.interface.ts"], "severity": "medium", "description": "接口 ParameterOptimizationResult 在2个文件中定义"}, {"pattern": "重复接口: ActionPlan", "files": ["src/contexts/learning/infrastructure/generators/improvement-suggestion-generator.ts", "src/contexts/ai-reasoning/domain/services/reasoning-traceability.interface.ts"], "severity": "medium", "description": "接口 ActionPlan 在2个文件中定义"}, {"pattern": "重复接口: Milestone", "files": ["src/contexts/learning/infrastructure/generators/improvement-suggestion-generator.ts", "src/contexts/ai-reasoning/domain/services/reasoning-traceability.interface.ts"], "severity": "medium", "description": "接口 Milestone 在2个文件中定义"}, {"pattern": "重复接口: TrendStrengthAnalysis", "files": ["src/contexts/learning/infrastructure/analyzers/trend-consistency-analyzer.ts", "src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts"], "severity": "medium", "description": "接口 TrendStrengthAnalysis 在2个文件中定义"}, {"pattern": "重复接口: MultiTimeframePredictions", "files": ["src/contexts/learning/infrastructure/analyzers/trend-consistency-analyzer.ts", "src/contexts/learning/infrastructure/analyzers/signal-quality-analyzer.ts"], "severity": "medium", "description": "接口 MultiTimeframePredictions 在2个文件中定义"}, {"pattern": "重复接口: HistoricalPerformance", "files": ["src/contexts/learning/infrastructure/analyzers/signal-quality-analyzer.ts", "src/contexts/ai-reasoning/domain/services/learning-knowledge-base.interface.ts"], "severity": "medium", "description": "接口 HistoricalPerformance 在2个文件中定义"}, {"pattern": "重复接口: QualityIssue", "files": ["src/contexts/market-data/infrastructure/external/real-time-data-quality-monitor.ts", "src/contexts/market-data/domain/services/real-time-data-quality-monitor.interface.ts", "src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts"], "severity": "medium", "description": "接口 QualityIssue 在3个文件中定义"}, {"pattern": "重复接口: PredictionResult", "files": ["src/contexts/market-data/infrastructure/external/predictive-data-preloader.ts", "src/contexts/ai-reasoning/domain/services/unified-learning-engine.interface.ts", "src/contexts/ai-reasoning/domain/services/short-cycle-learning.interface.ts"], "severity": "medium", "description": "接口 PredictionResult 在3个文件中定义"}, {"pattern": "重复接口: DistributionNode", "files": ["src/contexts/market-data/infrastructure/external/high-performance-data-distribution-network.ts", "src/contexts/market-data/domain/services/high-performance-data-distribution-network.interface.ts"], "severity": "medium", "description": "接口 DistributionNode 在2个文件中定义"}, {"pattern": "重复接口: DistributionRequest", "files": ["src/contexts/market-data/infrastructure/external/high-performance-data-distribution-network.ts", "src/contexts/market-data/domain/services/high-performance-data-distribution-network.interface.ts"], "severity": "medium", "description": "接口 DistributionRequest 在2个文件中定义"}, {"pattern": "重复接口: DistributionResult", "files": ["src/contexts/market-data/infrastructure/external/high-performance-data-distribution-network.ts", "src/contexts/market-data/domain/services/high-performance-data-distribution-network.interface.ts"], "severity": "medium", "description": "接口 DistributionResult 在2个文件中定义"}, {"pattern": "重复接口: DataRequest", "files": ["src/contexts/market-data/infrastructure/external/exchange-router.ts", "src/contexts/market-data/infrastructure/external/api-fallback-manager.ts", "src/contexts/market-data/domain/services/exchange-router.interface.ts"], "severity": "medium", "description": "接口 DataRequest 在3个文件中定义"}, {"pattern": "重复接口: DataSourceQuality", "files": ["src/contexts/market-data/infrastructure/external/data-source-priority-resolver.ts", "src/contexts/market-data/infrastructure/external/data-quality-analysis-engine.ts"], "severity": "medium", "description": "接口 DataSourceQuality 在2个文件中定义"}, {"pattern": "重复接口: QualityReport", "files": ["src/contexts/market-data/infrastructure/external/data-quality-analysis-engine.ts", "src/contexts/market-data/domain/services/real-time-data-quality-monitor.interface.ts"], "severity": "medium", "description": "接口 QualityReport 在2个文件中定义"}, {"pattern": "重复接口: FundingRateData", "files": ["src/contexts/market-data/infrastructure/external/binance-futures-adapter.ts", "src/contexts/market-data/domain/services/external-adapters/IBinanceFuturesAdapter.ts"], "severity": "medium", "description": "接口 FundingRateData 在2个文件中定义"}, {"pattern": "重复接口: OpenInterestData", "files": ["src/contexts/market-data/infrastructure/external/binance-futures-adapter.ts", "src/contexts/market-data/domain/services/external-adapters/IBinanceFuturesAdapter.ts"], "severity": "medium", "description": "接口 OpenInterestData 在2个文件中定义"}, {"pattern": "重复接口: LongShortRatioData", "files": ["src/contexts/market-data/infrastructure/external/binance-futures-adapter.ts", "src/contexts/market-data/domain/services/external-adapters/IBinanceFuturesAdapter.ts"], "severity": "medium", "description": "接口 LongShortRatioData 在2个文件中定义"}, {"pattern": "重复接口: DataQualityResult", "files": ["src/contexts/market-data/domain/services/real-time-data-quality-monitor.interface.ts", "src/contexts/trend-analysis/infrastructure/services/data-quality-validator.ts"], "severity": "medium", "description": "接口 DataQualityResult 在2个文件中定义"}, {"pattern": "重复接口: ExchangeConfig", "files": ["src/contexts/market-data/domain/services/exchange-router.interface.ts", "src/contexts/market-data/domain/services/exchange-adapter.ts"], "severity": "medium", "description": "接口 ExchangeConfig 在2个文件中定义"}, {"pattern": "重复接口: KlineData", "files": ["src/contexts/market-data/domain/services/exchange-adapter.ts", "src/contexts/trend-analysis/infrastructure/services/real-market-data-provider.ts"], "severity": "medium", "description": "接口 KlineData 在2个文件中定义"}, {"pattern": "重复接口: CrossTimeframeInsight", "files": ["src/contexts/ai-reasoning/infrastructure/services/multi-timeframe-learning-coordinator.ts", "src/contexts/ai-reasoning/domain/services/timeframe-coordinator.interface.ts"], "severity": "medium", "description": "接口 CrossTimeframeInsight 在2个文件中定义"}, {"pattern": "重复接口: LearningResult", "files": ["src/contexts/ai-reasoning/domain/services/unified-learning-engine.interface.ts", "src/contexts/ai-reasoning/domain/services/continuous-learning-engine.interface.ts"], "severity": "medium", "description": "接口 LearningResult 在2个文件中定义"}, {"pattern": "重复接口: ParameterAdjustment", "files": ["src/contexts/ai-reasoning/domain/services/short-cycle-learning.interface.ts", "src/contexts/ai-reasoning/domain/services/parameter-config-center.interface.ts"], "severity": "medium", "description": "接口 ParameterAdjustment 在2个文件中定义"}, {"pattern": "重复接口: PerformanceMetric", "files": ["src/contexts/ai-reasoning/domain/services/short-cycle-learning.interface.ts", "src/contexts/ai-reasoning/domain/services/parameter-config-center.interface.ts", "src/contexts/ai-reasoning/domain/services/learning-knowledge-base.interface.ts"], "severity": "medium", "description": "接口 PerformanceMetric 在3个文件中定义"}, {"pattern": "重复接口: LearningProgress", "files": ["src/contexts/ai-reasoning/domain/services/short-cycle-learning.interface.ts", "src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts"], "severity": "medium", "description": "接口 LearningProgress 在2个文件中定义"}, {"pattern": "重复接口: <PERSON>ing<PERSON><PERSON><PERSON>", "files": ["src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts", "src/contexts/ai-reasoning/domain/services/reasoning-chain.interface.ts"], "severity": "medium", "description": "接口 ReasoningChain 在2个文件中定义"}, {"pattern": "重复接口: ComplexQuery", "files": ["src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts", "src/contexts/ai-reasoning/domain/services/reasoning-chain.interface.ts"], "severity": "medium", "description": "接口 ComplexQuery 在2个文件中定义"}, {"pattern": "重复接口: SubQuery", "files": ["src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts", "src/contexts/ai-reasoning/domain/services/reasoning-chain.interface.ts"], "severity": "medium", "description": "接口 SubQuery 在2个文件中定义"}, {"pattern": "重复接口: Evidence", "files": ["src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts", "src/contexts/ai-reasoning/domain/services/reasoning-chain.interface.ts"], "severity": "medium", "description": "接口 Evidence 在2个文件中定义"}, {"pattern": "重复接口: ReasoningStep", "files": ["src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts", "src/contexts/ai-reasoning/domain/services/reasoning-chain.interface.ts"], "severity": "medium", "description": "接口 ReasoningStep 在2个文件中定义"}, {"pattern": "重复接口: Conclusion", "files": ["src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts", "src/contexts/ai-reasoning/domain/services/reasoning-chain.interface.ts"], "severity": "medium", "description": "接口 Conclusion 在2个文件中定义"}, {"pattern": "重复接口: QualityAssessment", "files": ["src/contexts/ai-reasoning/domain/services/llm-provider.interface.ts", "src/contexts/ai-reasoning/domain/services/learning-knowledge-base.interface.ts"], "severity": "medium", "description": "接口 QualityAssessment 在2个文件中定义"}, {"pattern": "重复接口: TrendQualityAssessment", "files": ["src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts", "src/contexts/trend-analysis/domain/services/trend-analysis-engine.interface.ts"], "severity": "medium", "description": "接口 TrendQualityAssessment 在2个文件中定义"}, {"pattern": "重复接口: PivotPoint", "files": ["src/contexts/trend-analysis/infrastructure/services/professional-pivot-detector.ts", "src/contexts/trend-analysis/infrastructure/services/pattern-detection-helpers.ts"], "severity": "medium", "description": "接口 PivotPoint 在2个文件中定义"}, {"pattern": "重复接口: PatternAnalysisData", "files": ["src/contexts/trend-analysis/infrastructure/services/ai-pattern-analyzer.ts", "src/contexts/trend-analysis/infrastructure/modules/ai-enhancement/pattern-ai-enhancer.ts"], "severity": "medium", "description": "接口 PatternAnalysisData 在2个文件中定义"}, {"pattern": "重复接口: AIEnhancedPattern", "files": ["src/contexts/trend-analysis/infrastructure/services/ai-pattern-analyzer.ts", "src/contexts/trend-analysis/infrastructure/modules/ai-enhancement/pattern-ai-enhancer.ts"], "severity": "medium", "description": "接口 AIEnhancedPattern 在2个文件中定义"}, {"pattern": "重复接口: RiskDataStrategyConfig", "files": ["src/shared/infrastructure/data-processing/strategies/risk/types.ts", "src/shared/infrastructure/data-processing/strategies/risk/risk-data-strategy.ts"], "severity": "medium", "description": "接口 RiskDataStrategyConfig 在2个文件中定义"}, {"pattern": "潜在重复服务: <PERSON><PERSON>", "files": ["src/scripts/verify-system-integration.ts", "src/shared/application/application-service-interfaces.ts", "src/express-app/modules/services-startup.ts", "src/shared/application/interfaces/application-service.ts", "src/shared/infrastructure/performance/unified-performance-manager.ts", "src/shared/infrastructure/performance/cache-manager.ts", "src/shared/infrastructure/market-data/unified-market-data-processor.ts", "src/shared/infrastructure/ai/multi-tier-cache-service.ts", "src/shared/infrastructure/ai/cached-ai-call-decorator.ts", "src/shared/infrastructure/cache/cache-performance-monitor.ts", "src/shared/infrastructure/cache/cache-consistency-manager.ts", "src/shared/infrastructure/monitoring/performance/slippage-monitor.ts", "src/shared/infrastructure/monitoring/performance/execution-latency-monitor.ts", "src/shared/infrastructure/di/types/service-types.ts", "src/shared/infrastructure/di/modules/market-data-container-module.ts", "src/shared/infrastructure/di/modules/infrastructure-container-module.ts", "src/shared/infrastructure/di/base/unified-symbol-registry.ts", "src/shared/infrastructure/di/base/dependency-resolution-strategy.ts", "src/shared/infrastructure/di/base/base-container-module.ts", "src/shared/infrastructure/config/dynamic/config-version-manager.ts", "src/contexts/trend-analysis/infrastructure/di/types.ts", "src/contexts/ai-reasoning/infrastructure/traceability/reasoning-process-recorder.ts", "src/contexts/ai-reasoning/infrastructure/traceability/decision-path-tracker.ts"], "severity": "medium", "description": "发现23个可能的Cache相关实现"}, {"pattern": "潜在重复服务: Database", "files": ["src/shared/infrastructure/ai/vector-database-service.ts", "src/shared/infrastructure/ai/multi-tier-cache-service.ts", "src/shared/infrastructure/monitoring/providers/database-health-provider.ts", "src/shared/infrastructure/di/types/service-types.ts", "src/shared/infrastructure/di/modules/infrastructure-container-module.ts", "src/contexts/trend-analysis/infrastructure/services/real-market-data-provider.ts", "src/contexts/trading-execution/infrastructure/services/symbol-validator.ts", "src/contexts/trading-execution/domain/services/webhook-alert-service.ts", "src/contexts/trading-execution/domain/services/dual-track-monitoring-service.ts", "src/contexts/trading-execution/domain/services/dual-environment-learning-service.ts", "src/contexts/market-data/infrastructure/external/database-query-index-optimizer.ts"], "severity": "medium", "description": "发现11个可能的Database相关实现"}, {"pattern": "潜在重复服务: Config", "files": ["src/scripts/verify-system-integration.ts", "src/scripts/simple-integration-check.ts", "src/config/constants.ts", "src/api/controllers/config-controller.ts", "src/shared/infrastructure/risk/risk-enforcement-config-manager.ts", "src/shared/infrastructure/performance/unified-performance-manager.ts", "src/shared/infrastructure/testing/unified-test-setup.ts", "src/shared/infrastructure/testing/unified-test-config-manager.ts", "src/shared/infrastructure/testing/unified-integration-test-base.ts", "src/shared/infrastructure/testing/index.ts", "src/shared/infrastructure/error/unified-error-handler.ts", "src/shared/infrastructure/error/error-handling-config.ts", "src/shared/infrastructure/data-processing/index.ts", "src/shared/infrastructure/config/unified-performance-config.ts", "src/shared/infrastructure/config/unified-default-config.ts", "src/shared/infrastructure/config/unified-config-manager.ts", "src/shared/infrastructure/config/risk-config.service.ts", "src/shared/infrastructure/config/config-monitor.ts", "src/shared/infrastructure/di/types/service-types.ts", "src/shared/infrastructure/di/types/context-types.ts", "src/shared/infrastructure/di/modules/user-config-container-module.ts", "src/shared/infrastructure/di/modules/risk-management-container-module.ts", "src/shared/infrastructure/di/modules/infrastructure-container-module.ts", "src/shared/infrastructure/di/modules/dynamic-config-container-module.ts", "src/shared/infrastructure/di/modules/config-container-module.ts", "src/shared/infrastructure/di/base/unified-symbol-registry.ts", "src/shared/infrastructure/di/base/service-factory.ts", "src/shared/infrastructure/di/base/dependency-resolution-strategy.ts", "src/shared/infrastructure/di/base/base-container-module.ts", "src/shared/infrastructure/data-processing/interfaces/IPipelineStageCoordinator.ts", "src/shared/infrastructure/config/validators/config-validator.ts", "src/shared/infrastructure/config/validation/config-validator.ts", "src/shared/infrastructure/config/validation/config-validation-rules.ts", "src/shared/infrastructure/config/dynamic/dynamic-config.interface.ts", "src/shared/infrastructure/config/dynamic/dynamic-config-manager.ts", "src/shared/infrastructure/config/dynamic/config-version-manager.ts", "src/shared/infrastructure/config/interfaces/index.ts", "src/shared/infrastructure/analysis/services/FinancialMetricsService.ts", "src/contexts/trend-analysis/infrastructure/services/volume-analyzer.ts", "src/contexts/trend-analysis/infrastructure/services/target-stop-calculator.ts", "src/contexts/trend-analysis/infrastructure/services/professional-pivot-detector.ts", "src/contexts/trend-analysis/infrastructure/adapters/signal-fusion-adapter.ts", "src/contexts/user-config/presentation/http/StatisticsController.ts", "src/contexts/user-config/infrastructure/services/EnhancedLLMRouter.ts", "src/contexts/user-config/infrastructure/services/ConfigHotReloadService.ts", "src/contexts/user-config/infrastructure/listeners/ConfigChangeListener.ts", "src/contexts/user-config/application/services/UserConfigService.ts", "src/contexts/user-config/application/services/ModelSelectionService.ts", "src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts", "src/contexts/ai-reasoning/infrastructure/config/performance-config.ts", "src/contexts/trading-signals/application/services/signal-generation-application-service.ts"], "severity": "medium", "description": "发现51个可能的Config相关实现"}, {"pattern": "潜在重复服务: Event", "files": ["src/shared/infrastructure/messaging/event-bus.ts", "src/application/event-broadcaster/events/event-processor.ts", "src/contexts/market-data/infrastructure/external/eventual-consistency-manager.ts"], "severity": "medium", "description": "发现3个可能的Event相关实现"}, {"pattern": "潜在重复服务: Validation", "files": ["src/shared/infrastructure/di/types/service-types.ts", "src/shared/infrastructure/di/types/context-types.ts", "src/shared/infrastructure/data-processing/executors/validation-stage-executor.ts", "src/contexts/market-data/application/services/weight-validation-service.ts"], "severity": "medium", "description": "发现4个可能的Validation相关实现"}]}