{"summary": {"totalFiles": 787, "checkedFiles": 787, "issuesFound": 0, "cleanupSuccess": true}, "issues": [], "deprecatedReferences": [], "movedFileReferences": [], "unifiedComponentUsage": [{"component": "UnifiedEnvironmentManager", "usageCount": 8, "files": ["src/shared/infrastructure/websocket/websocket-server.ts", "src/shared/infrastructure/ai/multi-tier-cache-service.ts", "src/shared/infrastructure/config/config-validation.ts", "src/shared/infrastructure/di/types/service-types.ts", "src/shared/infrastructure/di/modules/infrastructure-container-module.ts", "src/shared/infrastructure/config/environment/unified-environment-manager.ts", "src/shared/infrastructure/config/environment/environment-extensions.ts", "src/contexts/user-config/infrastructure/services/ConfigHotReloadService.ts"]}, {"component": "UnifiedConfigManager", "usageCount": 11, "files": ["src/scripts/verify-system-integration.ts", "src/config/constants.ts", "src/api/controllers/config-controller.ts", "src/shared/infrastructure/performance/unified-performance-manager.ts", "src/shared/infrastructure/config/unified-performance-config.ts", "src/shared/infrastructure/config/unified-config-manager.ts", "src/shared/infrastructure/di/types/service-types.ts", "src/shared/infrastructure/di/modules/infrastructure-container-module.ts", "src/contexts/trend-analysis/infrastructure/services/volume-analyzer.ts", "src/contexts/trend-analysis/infrastructure/services/target-stop-calculator.ts", "src/contexts/trend-analysis/infrastructure/services/professional-pivot-detector.ts"]}, {"component": "UnifiedAIServiceManager", "usageCount": 4, "files": ["src/shared/infrastructure/ai/unified-ai-service-manager.ts", "src/shared/infrastructure/ai/realtime-push-service.ts", "src/shared/infrastructure/di/types/service-types.ts", "src/shared/infrastructure/di/modules/infrastructure-container-module.ts"]}, {"component": "UnifiedDataMapper", "usageCount": 7, "files": ["src/shared/infrastructure/database/unified-data-mapper.ts", "src/shared/infrastructure/data-processing/data-processing-pipeline.ts", "src/shared/infrastructure/di/types/service-types.ts", "src/shared/infrastructure/data-processing/executors/mapping-stage-executor.ts", "src/contexts/user-management/infrastructure/repositories/UnifiedUserRepository.ts", "src/contexts/risk-management/infrastructure/repositories/UnifiedRiskAssessmentRepository.ts", "src/contexts/market-data/infrastructure/repositories/prisma-market-symbol-repository.ts"]}, {"component": "UnifiedLogger", "usageCount": 10, "files": ["src/config/logging.ts", "src/api/controllers/unified-health-controller.ts", "src/shared/infrastructure/performance/unified-performance-manager.ts", "src/shared/infrastructure/messaging/redis.ts", "src/shared/infrastructure/logging/unified-logger.ts", "src/shared/infrastructure/logging/logger-factory.ts", "src/shared/infrastructure/health/unified-health-service.ts", "src/shared/infrastructure/database/repository-base-service.ts", "src/shared/infrastructure/database/query-manager.ts", "src/shared/infrastructure/di/modules/infrastructure-container-module.ts"]}, {"component": "UnifiedMonitoringManager", "usageCount": 7, "files": ["src/api/routes/unified-health-routes.ts", "src/api/controllers/unified-health-controller.ts", "src/shared/infrastructure/performance/unified-performance-manager.ts", "src/shared/infrastructure/monitoring/unified-monitoring-manager.ts", "src/shared/infrastructure/database/query-manager.ts", "src/shared/infrastructure/di/types/service-types.ts", "src/shared/infrastructure/di/modules/infrastructure-container-module.ts"]}, {"component": "UnifiedLearningServiceManager", "usageCount": 4, "files": ["src/application/short-term-learning-service.ts", "src/application/long-term-learning-service.ts", "src/shared/infrastructure/learning/unified-learning-service-manager.ts", "src/contexts/ai-reasoning/infrastructure/services/unified-prediction-engine.ts"]}]}